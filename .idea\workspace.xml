<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="53bca7a6-a998-4a35-ab70-dab1c50302f0" name="更改" comment="refactor(docs): 重命名提交规范文档以准确反映其功能定位" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="GitHubPullRequestSearchHistory"><![CDATA[{
  "lastFilter": {
    "state": "OPEN",
    "assignee": "huazz233"
  }
}]]></component>
  <component name="GithubPullRequestsUISettings"><![CDATA[{
  "selectedUrlAndAccountId": {
    "url": "https://github.com/huazz233/huazz-rules.git",
    "accountId": "8a1340bc-98c1-467f-aa42-dddc91076c64"
  }
}]]></component>
  <component name="ProjectColorInfo"><![CDATA[{
  "associatedIndex": 5
}]]></component>
  <component name="ProjectId" id="30a8CGMyimOT8s4D3YQNuPoIvmZ" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "git-widget-placeholder": "master",
    "junie.onboarding.icon.badge.shown": "true",
    "last_opened_file_path": "C:/Users/<USER>/Desktop/huazz-rules/参考/工作流",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.stylelint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.stylelint": "",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="C:\Users\<USER>\Desktop\huazz-rules\参考\工作流" />
      <recent name="C:\Users\<USER>\Desktop\huazz-rules\参考\Blue-Soul-commits" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="C:\Users\<USER>\Desktop\huazz-rules\参考" />
      <recent name="C:\Users\<USER>\Desktop\huazz-rules\参考\cunzhi" />
    </key>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-d6986cc7102b-6a121458b545-JavaScript-PY-251.25410.122" />
        <option value="bundled-python-sdk-880ecab49056-36ea0e71a18c-com.jetbrains.pycharm.pro.sharedIndexes.bundled-PY-251.25410.122" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="53bca7a6-a998-4a35-ab70-dab1c50302f0" name="更改" comment="" />
      <created>1753854194159</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1753854194159</updated>
      <workItem from="1753854195270" duration="11487000" />
    </task>
    <task id="LOCAL-00001" summary="add ..">
      <option name="closed" value="true" />
      <created>1753856181859</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1753856181859</updated>
    </task>
    <task id="LOCAL-00002" summary="添加目前的mcp配置">
      <option name="closed" value="true" />
      <created>1753858473169</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1753858473169</updated>
    </task>
    <task id="LOCAL-00003" summary="整理">
      <option name="closed" value="true" />
      <created>1753861327484</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1753861327484</updated>
    </task>
    <task id="LOCAL-00004" summary="111">
      <option name="closed" value="true" />
      <created>1753865950972</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1753865950972</updated>
    </task>
    <task id="LOCAL-00005" summary="111">
      <option name="closed" value="true" />
      <created>1753866169059</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1753866169059</updated>
    </task>
    <task id="LOCAL-00006" summary="111">
      <option name="closed" value="true" />
      <created>1753866290003</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1753866290003</updated>
    </task>
    <option name="localTasksCounter" value="7" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="master" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
            </State>
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="add .." />
    <MESSAGE value="chore(mcp): 整理配置并完善文档&#10;&#10;精简 MCP 配置，重构介绍文档为结构化格式。" />
    <MESSAGE value="chore(mcp): 整理配置并完善文档&#10;&#10;精简 MCP 配置，重构介绍文档为结构化格式。" />
    <MESSAGE value="refactor(docs): 重构项目目录结构并统一资源来源说明&#10;&#10;将分散目录整合到&quot;参考资料&quot;下，统一资源来源文档，新增项目README。&#10;&#10;1. 目录重构: 整合&quot;参考&quot;、&quot;在用&quot;、&quot;猫娘&quot;等目录到&quot;参考资料&quot;下分类管理&#10;2. 文档整合: 合并各子目录来源文档为统一的&quot;参考资料/来源.md&quot;&#10;3. 项目文档: 新增根目录README.md" />
    <MESSAGE value="refactor(docs): 重构项目目录结构并统一资源来源说明&#10;&#10;将分散目录整合到&quot;参考资料&quot;下，统一资源来源文档，新增项目README。&#10;&#10;1. 目录重构: 整合&quot;参考&quot;、&quot;在用&quot;、&quot;猫娘&quot;等目录到&quot;参考资料&quot;下分类管理&#10;2. 文档整合: 合并各子目录来源文档为统一的&quot;参考资料/来源.md&quot;&#10;3. 项目文档: 新增根目录README.md" />
    <MESSAGE value="refactor(docs): 重构提交规范文档为AI工具执行指南&#10;&#10;将原有的团队Git协作规范重构为专门的AI助手工作指南，明确定义了AI在分析提交和生成规范信息时的执行原则、知识库和工作流程。&#10;&#10;1. 文档结构重组: 采用执行原则、知识库、记忆机制、使用方式四个核心部分&#10;2. 功能重新定位: 从团队规范转为AI工具的专用执行指南&#10;3. 工作流标准化: 明确定义了从接收输入到生成输出的完整流程&#10;4. 输出格式规范: 统一了提交信息和代码审查报告的标准格式&#10;5. 使用指南完善: 区分最新提交和历史提交的不同修改方式" />
    <MESSAGE value="refactor(docs): 重构提交规范文档为AI工具执行指南&#10;&#10;将原有的团队Git协作规范重构为专门的AI助手工作指南，明确定义了AI在分析提交和生成规范信息时的执行原则、知识库和工作流程。&#10;&#10;1. 文档结构重组: 采用执行原则、知识库、记忆机制、使用方式四个核心部分&#10;2. 功能重新定位: 从团队规范转为AI工具的专用执行指南&#10;3. 工作流标准化: 明确定义了从接收输入到生成输出的完整流程&#10;4. 输出格式规范: 统一了提交信息和代码审查报告的标准格式&#10;5. 使用指南完善: 区分最新提交和历史提交的不同修改方式" />
    <MESSAGE value="chore(docs): 删除提交规范文档&#10;&#10;移除了参考资料目录下的AI提交分析规范文档，该文件包含了AI工具的执行原则、知识库定义、记忆机制和使用方式等内容。&#10;&#10;1. 文件删除: 完全移除 参考资料/常用规则/提交规范.md 文件&#10;2. 内容清理: 删除了95行的AI工具执行指南内容&#10;3. 目录整理: 清理常用规则目录下的相关文档" />
    <MESSAGE value="refactor(docs): 重命名提交规范文档以准确反映其功能定位" />
    <MESSAGE value="refactor(docs): 重命名提交规范文档以准确反映其功能定位" />
    <option name="LAST_COMMIT_MESSAGE" value="refactor(docs): 重命名提交规范文档以准确反映其功能定位" />
  </component>
</project>