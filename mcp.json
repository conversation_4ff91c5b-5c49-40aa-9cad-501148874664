{"mcpServers": {"Sequential Thinking Tools": {"type": "stdio", "command": "npx", "args": ["-y", "@smithery/cli@latest", "run", "@xinzhongyouhai/mcp-sequentialthinking-tools", "--key", "214acdef-eba5-4cf8-badc-271d310f4c85"], "env": {}}, "context7": {"type": "stdio", "command": "npx", "args": ["-y", "@upstash/context7-mcp@latest"]}, "deepwiki": {"type": "sse", "url": "https://mcp.deepwiki.com/sse"}, "searxng": {"command": "mcp-searxng", "env": {"SEARXNG_URL": "https://searx.stream/"}}, "shrimp-task-manager": {"command": "npx", "args": ["-y", "mcp-shrimp-task-manager"], "env": {"DATA_DIR": "C:\\Users\\<USER>\\Desktop\\cursor-shrimp", "TEMPLATES_USE": "zh", "ENABLE_GUI": "false"}}, "mcp-feedback-enhanced": {"command": "uvx", "args": ["mcp-feedback-enhanced@latest"], "timeout": 600, "env": {"MCP_DESKTOP_MODE": "true", "MCP_WEB_HOST": "127.0.0.1", "MCP_WEB_PORT": "8765", "MCP_DEBUG": "false"}, "autoApprove": ["interactive_feedback"]}, "desktop-commander": {"command": "npx", "args": ["-y", "@wonderwhy-er/desktop-commander"]}}}