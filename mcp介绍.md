# MCP (Model Context Protocol) 介绍

MCP 是一种协议，允许 AI 模型通过标准化接口与外部工具和服务进行交互。通过 MCP，AI 可以访问实时数据、执行系统操作、管理任务等，大大扩展了 AI 的能力边界。

## 可用 MCP 工具概览

| 工具名 | 缩写 | 核心功能 |
|--------|------|----------|
| AugmentContextEngine | ACE | 上下文感知: 分析项目代码与结构 (默认功能) |
| Context7 | - | 权威查询: 查询库/框架的最新官方文档、API 细节和代码示例 |
| sequential-thinking | - | 思维链: 结构化思考与规划，最高优先级 |
| searxng | - | 联网搜索: 搜索广泛的公开网络信息 |
| deepwiki | - | GitHub 代码溯源: 查询 GitHub 仓库的公开信息 |
| shrimp-task-manager | - | 任务管理: 规划和执行多步任务，按需激活 |
| mcp-feedback-enhanced | - | 用户交互: 在关键节点暂停并获取用户确认，默认等待 600 秒 |
| desktop-commander | - | 系统文件操作和命令执行 |

## MCP 工具安装方式

### searxng (联网搜索)
- **项目地址**: https://github.com/ihor-sokoliuk/mcp-searxng
- **安装命令**: `npm install -g mcp-searxng`
- **获取 SEARXNG_URL**: https://searx.space/

### desktop-commander (系统操作)
- **安装命令**: `npm install -g @wonderwhy-er/desktop-commander`

### shrimp-task-manager (任务管理)
- **项目地址**: https://github.com/cjo4m06/mcp-shrimp-task-manager/blob/main/docs/zh/README.md
- **说明**: 
  - DATA_DIR: 指定数据存储目录，用于保存任务管理相关的数据文件
  - 当前配置路径: `C:\Users\<USER>\Desktop\cursor-shrimp`
  - TEMPLATES_USE: 模板语言设置，"zh" 表示使用中文模板
  - ENABLE_GUI: 是否启用图形界面，"false" 表示仅使用命令行模式

### Sequential Thinking Tools (思维链)
- **说明**: key 为授权密钥，用于访问思维工具服务

### mcp-feedback-enhanced (用户交互)
- **项目地址**: https://github.com/Minidoracat/mcp-feedback-enhanced/blob/main/README.zh-CN.md

## mcp.json 配置文件

```json
{
  "mcpServers": {
    "Sequential Thinking Tools": {
      "type": "stdio",
      "command": "npx",
      "args": [
        "-y",
        "@smithery/cli@latest",
        "run",
        "@xinzhongyouhai/mcp-sequentialthinking-tools",
        "--key",
        "214acdef-eba5-4cf8-badc-271d310f4c85"
      ],
      "env": {}
    },
    "context7": {
      "type": "stdio",
      "command": "npx",
      "args": [
        "-y",
        "@upstash/context7-mcp@latest"
      ]
    },
    "deepwiki": {
      "type": "sse",
      "url": "https://mcp.deepwiki.com/sse"
    },
    "searxng": {
      "command": "mcp-searxng",
      "env": {
        "SEARXNG_URL": "https://searx.stream/"
      }
    },
    "shrimp-task-manager": {
      "command": "npx",
      "args": [
        "-y",
        "mcp-shrimp-task-manager"
      ],
      "env": {
        "DATA_DIR": "C:\\Users\\<USER>\\Desktop\\cursor-shrimp",
        "TEMPLATES_USE": "zh",
        "ENABLE_GUI": "false"
      }
    },
    "mcp-feedback-enhanced": {
      "command": "uvx",
      "args": ["mcp-feedback-enhanced@latest"],
      "timeout": 600,
      "env": {
        "MCP_DESKTOP_MODE": "true",
        "MCP_WEB_HOST": "127.0.0.1",
        "MCP_WEB_PORT": "8765",
        "MCP_DEBUG": "false"
      },
      "autoApprove": ["interactive_feedback"]
    },
    "desktop-commander": {
      "command": "npx",
      "args": [
        "-y",
        "@wonderwhy-er/desktop-commander"
      ]
    }
  }
}
```