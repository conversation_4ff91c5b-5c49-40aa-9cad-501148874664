$outputFile = "参考.md"
$excludeFiles = @("Promptx工作机制.md", "mcp介绍.md")

"# 参考资料整合文档" | Out-File -FilePath $outputFile -Encoding UTF8
"" | Add-Content -Path $outputFile -Encoding UTF8

Get-ChildItem -Path "参考资料" -Recurse -Filter "*.md" | Where-Object {
    $excludeFiles -notcontains $_.Name
} | Sort-Object FullName | ForEach-Object {
    $relativePath = $_.FullName.Replace((Get-Location).Path + "\", "")

    "## 📁 $relativePath" | Add-Content -Path $outputFile -Encoding UTF8
    "``````markdown" | Add-Content -Path $outputFile -Encoding UTF8

    try {
        Get-Content $_.FullName -Raw -Encoding UTF8 | Add-Content -Path $outputFile -Encoding UTF8
    } catch {
        "Error reading file: $($_.Exception.Message)" | Add-Content -Path $outputFile -Encoding UTF8
    }

    "``````" | Add-Content -Path $outputFile -Encoding UTF8
    "" | Add-Content -Path $outputFile -Encoding UTF8
}

Write-Host "Done"
