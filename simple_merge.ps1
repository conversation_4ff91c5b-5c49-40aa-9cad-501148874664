$files = @(
    "参考资料\interactive-feedback mcp使用工作流.md",
    "参考资料\其他参考.md",
    "参考资料\来源.md",
    "参考资料\AI-Prompts\README.md",
    "参考资料\AI-Prompts\prompt-note.md",
    "参考资料\AI-Prompts\prompt增强版.md",
    "参考资料\AI-Prompts\提示词生成角色.md",
    "参考资料\其他工具\cunzhi.md",
    "参考资料\工作流程\Augment Code 工作模式规范 (2025-06-03).md",
    "参考资料\工作流程\Augment Code 工作模式规范 (2025-06-18).md",
    "参考资料\工作流程\Augment Code 工作模式规范 猫娘版(2025-06-23).md",
    "参考资料\工作流程\代码编辑任务工作流.md",
    "参考资料\工作流程\技术研究任务工作流.md",
    "参考资料\工作流程\问题调试任务工作流.md",
    "参考资料\工作流程\项目部署任务工作流.md",
    "参考资料\常用规则\AI提交分析规范.md",
    "参考资料\常用规则\主要提示词.md",
    "参考资料\常用规则\使用指南.md",
    "参考资料\提示词模板\猫娘v1.2.md",
    "参考资料\提示词模板\猫娘v2.1.md",
    "参考资料\提示词模板\猫娘开发提示词.md"
)

$output = "# 参考资料整合文档`n`n"

foreach ($file in $files) {
    if (Test-Path $file) {
        $filename = Split-Path $file -Leaf
        $output += "## 📁 $filename`n"
        $output += "``````markdown`n"
        $content = Get-Content $file -Raw -Encoding UTF8
        $output += $content
        $output += "`n``````"
        $output += "`n`n"
    }
}

[System.IO.File]::WriteAllText("参考.md", $output, [System.Text.Encoding]::UTF8)
